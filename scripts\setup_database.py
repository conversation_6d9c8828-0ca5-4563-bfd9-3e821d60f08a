import os
import sys
import json
import sqlite3
import logging

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database connection
DB_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'backend/instance/religious_experiences.db')

def ensure_db_connection():
    """Ensure database connection and return connection object"""
    os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    return conn

def create_tables():
    """Create database tables"""
    logger.info("Creating database tables...")
    
    conn = ensure_db_connection()
    cursor = conn.cursor()
    
    # Create users table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL,
            email TEXT NOT NULL UNIQUE,
            password TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Create religious_texts table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS religious_texts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            description TEXT,
            tradition TEXT NOT NULL,
            year_composed TEXT,
            original_language TEXT,
            path TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Create text_contents table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS text_contents (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            text_id INTEGER NOT NULL,
            language TEXT NOT NULL,
            content_type TEXT NOT NULL,
            content TEXT NOT NULL,
            metadata TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (text_id) REFERENCES religious_texts (id)
        )
    ''')
    
    # Create text_translations table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS text_translations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            text_id INTEGER NOT NULL,
            source_language TEXT NOT NULL,
            target_language TEXT NOT NULL,
            translator TEXT,
            translation_date TEXT,
            content TEXT NOT NULL,
            alignment_data TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (text_id) REFERENCES religious_texts (id)
        )
    ''')
    
    # Create text_sections table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS text_sections (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            text_id INTEGER NOT NULL,
            parent_id INTEGER,
            section_type TEXT NOT NULL,
            section_number TEXT NOT NULL,
            title TEXT,
            content_start_index INTEGER,
            content_end_index INTEGER,
            metadata TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (text_id) REFERENCES religious_texts (id),
            FOREIGN KEY (parent_id) REFERENCES text_sections (id)
        )
    ''')
    
    # Create text_alignments table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS text_alignments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            translation_id INTEGER NOT NULL,
            source_section_id INTEGER NOT NULL,
            target_section_id INTEGER NOT NULL,
            alignment_type TEXT NOT NULL,
            source_start_index INTEGER,
            source_end_index INTEGER,
            target_start_index INTEGER,
            target_end_index INTEGER,
            metadata TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (translation_id) REFERENCES text_translations (id),
            FOREIGN KEY (source_section_id) REFERENCES text_sections (id),
            FOREIGN KEY (target_section_id) REFERENCES text_sections (id)
        )
    ''')
    
    # Create surveys table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS surveys (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            religious_affiliation TEXT,
            geographical_location TEXT,
            divine_experience TEXT,
            divine_experience_frequency TEXT,
            divine_experience_significance TEXT,
            truth_perception TEXT,
            truth_confidence_level TEXT,
            practice_details TEXT,
            practice_frequency TEXT,
            practice_importance TEXT,
            additional_comments TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')
    
    conn.commit()
    conn.close()
    
    logger.info("Database tables created successfully")

def add_sample_data():
    """Add sample data to database"""
    logger.info("Adding sample data to database...")
    
    conn = ensure_db_connection()
    cursor = conn.cursor()
    
    # Add sample users
    cursor.execute("SELECT COUNT(*) FROM users")
    if cursor.fetchone()[0] == 0:
        cursor.execute('''
            INSERT INTO users (username, email, password)
            VALUES (?, ?, ?)
        ''', ('admin', '<EMAIL>', 'password'))  # In production, use password hashing
        
        logger.info("Added sample user")
    
    # Add sample religious texts
    cursor.execute("SELECT COUNT(*) FROM religious_texts")
    if cursor.fetchone()[0] == 0:
        religions = [
            {
                'name': 'Christianity',
                'texts': [
                    {
                        'title': 'Bible',
                        'description': 'The Christian Bible',
                        'tradition': 'Christianity',
                        'year_composed': '1st-4th century CE',
                        'original_language': 'Hebrew/Greek',
                        'path': 'texts/christian'
                    }
                ]
            },
            {
                'name': 'Judaism',
                'texts': [
                    {
                        'title': 'Torah',
                        'description': 'The first five books of the Hebrew Bible',
                        'tradition': 'Judaism',
                        'year_composed': '6th-5th century BCE',
                        'original_language': 'Hebrew',
                        'path': 'texts/hebrew'
                    }
                ]
            },
            {
                'name': 'Islam',
                'texts': [
                    {
                        'title': 'Quran',
                        'description': 'The central religious text of Islam',
                        'tradition': 'Islam',
                        'year_composed': '7th century CE',
                        'original_language': 'Arabic',
                        'path': 'texts/islamic'
                    }
                ]
            },
            {
                'name': 'Hinduism',
                'texts': [
                    {
                        'title': 'Bhagavad Gita',
                        'description': 'A 700-verse Hindu scripture that is part of the epic Mahabharata',
                        'tradition': 'Hinduism',
                        'year_composed': '5th-2nd century BCE',
                        'original_language': 'Sanskrit',
                        'path': 'texts/hindu'
                    },
                    {
                        'title': 'Rigveda',
                        'description': 'An ancient Indian collection of Vedic Sanskrit hymns',
                        'tradition': 'Hinduism',
                        'year_composed': '1500-1200 BCE',
                        'original_language': 'Sanskrit',
                        'path': 'texts/hindu'
                    }
                ]
            },
            {
                'name': 'Buddhism',
                'texts': [
                    {
                        'title': 'Dhammapada',
                        'description': 'A collection of sayings of the Buddha in verse form',
                        'tradition': 'Buddhism',
                        'year_composed': '3rd century BCE',
                        'original_language': 'Pali',
                        'path': 'texts/buddhist'
                    }
                ]
            },
            {
                'name': 'Gnosticism',
                'texts': [
                    {
                        'title': 'Nag Hammadi Library',
                        'description': 'A collection of Gnostic texts discovered near Nag Hammadi, Egypt',
                        'tradition': 'Gnosticism',
                        'year_composed': '2nd-4th century CE',
                        'original_language': 'Coptic',
                        'path': 'texts/gnostic'
                    }
                ]
            },
            {
                'name': 'Zoroastrianism',
                'texts': [
                    {
                        'title': 'Avesta',
                        'description': 'The primary collection of religious texts of Zoroastrianism',
                        'tradition': 'Zoroastrianism',
                        'year_composed': '6th century BCE',
                        'original_language': 'Avestan',
                        'path': 'texts/zoroastrian'
                    }
                ]
            }
        ]
        
        for religion in religions:
            for text_info in religion['texts']:
                cursor.execute('''
                    INSERT INTO religious_texts (title, description, tradition, year_composed, original_language, path)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    text_info['title'],
                    text_info['description'],
                    text_info['tradition'],
                    text_info['year_composed'],
                    text_info['original_language'],
                    text_info['path']
                ))
        
        logger.info("Added sample religious texts")
    
    # Add sample content and translations
    cursor.execute("SELECT COUNT(*) FROM text_contents")
    if cursor.fetchone()[0] == 0:
        cursor.execute("SELECT id, title, original_language FROM religious_texts")
        texts = cursor.fetchall()
        
        for text in texts:
            text_id = text[0]
            title = text[1]
            original_language = text[2]
            
            # Add sample content
            cursor.execute('''
                INSERT INTO text_contents (text_id, language, content_type, content, metadata)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                text_id,
                original_language,
                'original',
                f'Sample content for {title} in {original_language}',
                json.dumps({
                    'source': 'Sample data',
                    'version': '1.0'
                })
            ))
            
            # Add English content
            cursor.execute('''
                INSERT INTO text_contents (text_id, language, content_type, content, metadata)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                text_id,
                'English',
                'translation',
                f'Sample English translation of {title}',
                json.dumps({
                    'source': 'Sample data',
                    'version': '1.0'
                })
            ))
            
            # Add sample translation
            cursor.execute('''
                INSERT INTO text_translations (text_id, source_language, target_language, translator, translation_date, content, alignment_data)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                text_id,
                original_language,
                'English',
                'Sample Translator',
                '2025',
                f'Sample translation of {title} from {original_language} to English',
                json.dumps({
                    'type': 'line-by-line',
                    'mapping': [
                        {'source_id': 1, 'target_id': 1},
                        {'source_id': 2, 'target_id': 2}
                    ]
                })
            ))
        
        logger.info("Added sample content and translations")
    
    conn.commit()
    conn.close()
    
    logger.info("Sample data added successfully")

def main():
    """Main function"""
    # First, remove existing database if it exists
    if os.path.exists(DB_PATH):
        logger.info(f"Removing existing database: {DB_PATH}")
        os.remove(DB_PATH)
    
    create_tables()
    add_sample_data()

if __name__ == "__main__":
    main()
