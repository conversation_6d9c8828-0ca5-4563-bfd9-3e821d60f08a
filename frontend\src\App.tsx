import { useState, useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom'
import axios from 'axios'
import './App.css'

// API base URL
const API_BASE_URL = 'http://localhost:5000/api'

// Types
interface ReligiousText {
  id: number
  title: string
  description: string
  tradition: string
  year_composed: string
  original_language: string
}

interface User {
  id: number
  username: string
  email: string
}

// Components
function Home() {
  const [texts, setTexts] = useState<ReligiousText[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchTexts()
  }, [])

  const fetchTexts = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/texts/`)
      setTexts(response.data.texts)
    } catch (err) {
      setError('Failed to fetch religious texts')
      console.error('Error fetching texts:', err)
    } finally {
      setLoading(false)
    }
  }

  if (loading) return <div className="loading">Loading...</div>
  if (error) return <div className="error">Error: {error}</div>

  return (
    <div className="home">
      <div className="hero-section">
        <h1>Religious Experiences App</h1>
        <p>Explore sacred texts from diverse traditions and share your spiritual journey</p>
        <div className="hero-stats">
          <div className="stat-item">
            <span className="stat-number">{texts.length}</span>
            <span className="stat-label">Sacred Texts</span>
          </div>
          <div className="stat-item">
            <span className="stat-number">8</span>
            <span className="stat-label">Traditions</span>
          </div>
          <div className="stat-item">
            <span className="stat-number">∞</span>
            <span className="stat-label">Wisdom</span>
          </div>
        </div>
      </div>

      <section className="texts-section">
        <h2>📚 Sacred Texts Collection</h2>
        {texts.length === 0 ? (
          <div className="no-texts-message">
            <p>🔄 No texts available. Please run the database setup script.</p>
          </div>
        ) : (
          <div className="texts-grid">
            {texts.map((text) => (
              <div key={text.id} className="text-card">
                <div className="card-header">
                  <h3>{text.title}</h3>
                  <div className="tradition-badge">{text.tradition}</div>
                </div>
                <p className="description">{text.description}</p>
                <div className="text-meta">
                  <span>🌍 {text.original_language}</span>
                  <span>📅 {text.year_composed}</span>
                </div>
              </div>
            ))}
          </div>
        )}
      </section>
    </div>
  )
}

function Survey() {
  const [formData, setFormData] = useState({
    religious_affiliation: '',
    geographical_location: '',
    divine_experience: '',
    divine_experience_frequency: '',
    divine_experience_significance: '',
    truth_perception: '',
    truth_confidence_level: '',
    practice_details: '',
    practice_frequency: '',
    practice_importance: '',
    additional_comments: ''
  })
  const [submitting, setSubmitting] = useState(false)
  const [submitted, setSubmitted] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSubmitting(true)

    try {
      await axios.post(`${API_BASE_URL}/survey/`, formData)
      setSubmitted(true)
    } catch (err) {
      console.error('Error submitting survey:', err)
      alert('Failed to submit survey. Please try again.')
    } finally {
      setSubmitting(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  if (submitted) {
    return (
      <div className="survey-success">
        <h2>Thank you!</h2>
        <p>Your survey has been submitted successfully.</p>
        <Link to="/">Return to Home</Link>
      </div>
    )
  }

  return (
    <div className="survey">
      <h1>Religious Experience Survey</h1>
      <p>Share your spiritual experiences and help us understand religious diversity.</p>

      <form onSubmit={handleSubmit} className="survey-form">
        <div className="form-group">
          <label htmlFor="religious_affiliation">Religious Affiliation:</label>
          <input
            type="text"
            id="religious_affiliation"
            name="religious_affiliation"
            value={formData.religious_affiliation}
            onChange={handleChange}
            placeholder="e.g., Christianity, Islam, Buddhism, etc."
          />
        </div>

        <div className="form-group">
          <label htmlFor="geographical_location">Geographical Location:</label>
          <input
            type="text"
            id="geographical_location"
            name="geographical_location"
            value={formData.geographical_location}
            onChange={handleChange}
            placeholder="e.g., United States, India, etc."
          />
        </div>

        <div className="form-group">
          <label htmlFor="divine_experience">Describe any divine or spiritual experiences:</label>
          <textarea
            id="divine_experience"
            name="divine_experience"
            value={formData.divine_experience}
            onChange={handleChange}
            rows={4}
            placeholder="Please describe any spiritual or divine experiences you've had..."
          />
        </div>

        <div className="form-group">
          <label htmlFor="divine_experience_frequency">How often do you have such experiences?</label>
          <select
            id="divine_experience_frequency"
            name="divine_experience_frequency"
            value={formData.divine_experience_frequency}
            onChange={handleChange}
          >
            <option value="">Select frequency</option>
            <option value="daily">Daily</option>
            <option value="weekly">Weekly</option>
            <option value="monthly">Monthly</option>
            <option value="yearly">Yearly</option>
            <option value="rarely">Rarely</option>
            <option value="never">Never</option>
          </select>
        </div>

        <div className="form-group">
          <label htmlFor="truth_perception">How do you perceive religious truth?</label>
          <textarea
            id="truth_perception"
            name="truth_perception"
            value={formData.truth_perception}
            onChange={handleChange}
            rows={3}
            placeholder="Your thoughts on religious truth and understanding..."
          />
        </div>

        <div className="form-group">
          <label htmlFor="practice_details">Describe your religious practices:</label>
          <textarea
            id="practice_details"
            name="practice_details"
            value={formData.practice_details}
            onChange={handleChange}
            rows={3}
            placeholder="Prayer, meditation, rituals, etc."
          />
        </div>

        <div className="form-group">
          <label htmlFor="additional_comments">Additional Comments:</label>
          <textarea
            id="additional_comments"
            name="additional_comments"
            value={formData.additional_comments}
            onChange={handleChange}
            rows={3}
            placeholder="Any other thoughts you'd like to share..."
          />
        </div>

        <button type="submit" disabled={submitting} className="submit-btn">
          {submitting ? 'Submitting...' : 'Submit Survey'}
        </button>
      </form>
    </div>
  )
}

function App() {
  return (
    <Router>
      <div className="app">
        <nav className="navbar">
          <div className="nav-container">
            <Link to="/" className="nav-logo">Religious Experiences</Link>
            <div className="nav-links">
              <Link to="/">Home</Link>
              <Link to="/survey">Survey</Link>
            </div>
          </div>
        </nav>

        <main className="main-content">
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/survey" element={<Survey />} />
          </Routes>
        </main>

        <footer className="footer">
          <p>&copy; 2025 Religious Experiences App. Built for academic research.</p>
        </footer>
      </div>
    </Router>
  )
}

export default App
