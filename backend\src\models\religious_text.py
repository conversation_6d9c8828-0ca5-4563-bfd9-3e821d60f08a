from datetime import datetime
import json
from . import db


class ReligiousText(db.Model):
    __tablename__ = 'religious_texts'

    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    tradition = db.Column(db.String(100), nullable=False)
    year_composed = db.Column(db.String(100))
    original_language = db.Column(db.String(50))
    path = db.Column(db.String(500))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    contents = db.relationship('TextContent', backref='religious_text', lazy=True, cascade='all, delete-orphan')
    translations = db.relationship('TextTranslation', backref='religious_text', lazy=True, cascade='all, delete-orphan')
    sections = db.relationship('TextSection', backref='religious_text', lazy=True, cascade='all, delete-orphan')

    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'tradition': self.tradition,
            'year_composed': self.year_composed,
            'original_language': self.original_language,
            'path': self.path,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

    def __repr__(self):
        return f'<ReligiousText {self.title}>'


class TextContent(db.Model):
    __tablename__ = 'text_contents'

    id = db.Column(db.Integer, primary_key=True)
    text_id = db.Column(db.Integer, db.ForeignKey('religious_texts.id'), nullable=False)
    language = db.Column(db.String(50), nullable=False)
    content_type = db.Column(db.String(50), nullable=False)  # 'original', 'translation', 'full', 'excerpt'
    content = db.Column(db.Text, nullable=False)
    meta_data = db.Column(db.Text)  # JSON string
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def get_metadata(self):
        """Get metadata as dictionary"""
        if self.meta_data:
            try:
                return json.loads(self.meta_data)
            except json.JSONDecodeError:
                return {}
        return {}

    def set_metadata(self, metadata_dict):
        """Set metadata from dictionary"""
        self.meta_data = json.dumps(metadata_dict)

    def to_dict(self):
        return {
            'id': self.id,
            'text_id': self.text_id,
            'language': self.language,
            'content_type': self.content_type,
            'content': self.content,
            'metadata': self.get_metadata(),
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

    def __repr__(self):
        return f'<TextContent {self.language} - {self.content_type}>'


class TextTranslation(db.Model):
    __tablename__ = 'text_translations'

    id = db.Column(db.Integer, primary_key=True)
    text_id = db.Column(db.Integer, db.ForeignKey('religious_texts.id'), nullable=False)
    source_language = db.Column(db.String(50), nullable=False)
    target_language = db.Column(db.String(50), nullable=False)
    translator = db.Column(db.String(200))
    translation_date = db.Column(db.String(50))
    content = db.Column(db.Text, nullable=False)
    alignment_data = db.Column(db.Text)  # JSON string
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def get_alignment_data(self):
        """Get alignment data as dictionary"""
        if self.alignment_data:
            try:
                return json.loads(self.alignment_data)
            except json.JSONDecodeError:
                return {}
        return {}

    def set_alignment_data(self, alignment_dict):
        """Set alignment data from dictionary"""
        self.alignment_data = json.dumps(alignment_dict)

    def to_dict(self):
        return {
            'id': self.id,
            'text_id': self.text_id,
            'source_language': self.source_language,
            'target_language': self.target_language,
            'translator': self.translator,
            'translation_date': self.translation_date,
            'content': self.content,
            'alignment_data': self.get_alignment_data(),
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

    def __repr__(self):
        return f'<TextTranslation {self.source_language} -> {self.target_language}>'


class TextSection(db.Model):
    __tablename__ = 'text_sections'

    id = db.Column(db.Integer, primary_key=True)
    text_id = db.Column(db.Integer, db.ForeignKey('religious_texts.id'), nullable=False)
    parent_id = db.Column(db.Integer, db.ForeignKey('text_sections.id'), nullable=True)
    section_type = db.Column(db.String(50), nullable=False)  # 'book', 'chapter', 'verse', 'paragraph'
    section_number = db.Column(db.String(50))
    title = db.Column(db.String(500))
    content = db.Column(db.Text)
    meta_data = db.Column(db.Text)  # JSON string
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Self-referential relationship for hierarchical structure
    children = db.relationship('TextSection', backref=db.backref('parent', remote_side=[id]), lazy=True)

    def get_metadata(self):
        """Get metadata as dictionary"""
        if self.meta_data:
            try:
                return json.loads(self.meta_data)
            except json.JSONDecodeError:
                return {}
        return {}

    def set_metadata(self, metadata_dict):
        """Set metadata from dictionary"""
        self.meta_data = json.dumps(metadata_dict)

    def to_dict(self):
        return {
            'id': self.id,
            'text_id': self.text_id,
            'parent_id': self.parent_id,
            'section_type': self.section_type,
            'section_number': self.section_number,
            'title': self.title,
            'content': self.content,
            'metadata': self.get_metadata(),
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

    def __repr__(self):
        return f'<TextSection {self.section_type} - {self.title}>'
