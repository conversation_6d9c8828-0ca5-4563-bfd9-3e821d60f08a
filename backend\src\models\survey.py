from datetime import datetime
from . import db


class Survey(db.Model):
    __tablename__ = 'surveys'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    user_id = db.<PERSON>umn(db.<PERSON><PERSON><PERSON>, db.<PERSON>('users.id'), nullable=True)
    
    # Survey fields
    religious_affiliation = db.Column(db.String(200))
    geographical_location = db.Column(db.String(200))
    divine_experience = db.Column(db.Text)
    divine_experience_frequency = db.Column(db.String(100))
    divine_experience_significance = db.Column(db.String(100))
    truth_perception = db.Column(db.Text)
    truth_confidence_level = db.Column(db.String(100))
    practice_details = db.Column(db.Text)
    practice_frequency = db.Column(db.String(100))
    practice_importance = db.Column(db.String(100))
    additional_comments = db.Column(db.Text)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'religious_affiliation': self.religious_affiliation,
            'geographical_location': self.geographical_location,
            'divine_experience': self.divine_experience,
            'divine_experience_frequency': self.divine_experience_frequency,
            'divine_experience_significance': self.divine_experience_significance,
            'truth_perception': self.truth_perception,
            'truth_confidence_level': self.truth_confidence_level,
            'practice_details': self.practice_details,
            'practice_frequency': self.practice_frequency,
            'practice_importance': self.practice_importance,
            'additional_comments': self.additional_comments,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def __repr__(self):
        return f'<Survey {self.id} - {self.religious_affiliation}>'
