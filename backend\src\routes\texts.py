from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from src.models import db, ReligiousText, TextContent, TextTranslation, TextSection

texts_bp = Blueprint('texts', __name__)


@texts_bp.route('/', methods=['GET'])
def get_texts():
    """Get all religious texts"""
    try:
        texts = ReligiousText.query.all()
        return jsonify({
            'texts': [text.to_dict() for text in texts]
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@texts_bp.route('/<int:text_id>', methods=['GET'])
def get_text(text_id):
    """Get a specific religious text"""
    try:
        text = ReligiousText.query.get(text_id)
        if not text:
            return jsonify({'error': 'Text not found'}), 404
        
        return jsonify({'text': text.to_dict()}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@texts_bp.route('/<int:text_id>/contents', methods=['GET'])
def get_text_contents(text_id):
    """Get contents for a specific text"""
    try:
        text = ReligiousText.query.get(text_id)
        if not text:
            return jsonify({'error': 'Text not found'}), 404
        
        language = request.args.get('language')
        content_type = request.args.get('content_type')
        
        query = TextContent.query.filter_by(text_id=text_id)
        
        if language:
            query = query.filter_by(language=language)
        if content_type:
            query = query.filter_by(content_type=content_type)
        
        contents = query.all()
        
        return jsonify({
            'contents': [content.to_dict() for content in contents]
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@texts_bp.route('/<int:text_id>/translations', methods=['GET'])
def get_text_translations(text_id):
    """Get translations for a specific text"""
    try:
        text = ReligiousText.query.get(text_id)
        if not text:
            return jsonify({'error': 'Text not found'}), 404
        
        source_language = request.args.get('source_language')
        target_language = request.args.get('target_language')
        
        query = TextTranslation.query.filter_by(text_id=text_id)
        
        if source_language:
            query = query.filter_by(source_language=source_language)
        if target_language:
            query = query.filter_by(target_language=target_language)
        
        translations = query.all()
        
        return jsonify({
            'translations': [translation.to_dict() for translation in translations]
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@texts_bp.route('/<int:text_id>/sections', methods=['GET'])
def get_text_sections(text_id):
    """Get sections for a specific text"""
    try:
        text = ReligiousText.query.get(text_id)
        if not text:
            return jsonify({'error': 'Text not found'}), 404
        
        section_type = request.args.get('section_type')
        parent_id = request.args.get('parent_id')
        
        query = TextSection.query.filter_by(text_id=text_id)
        
        if section_type:
            query = query.filter_by(section_type=section_type)
        if parent_id:
            query = query.filter_by(parent_id=int(parent_id))
        elif parent_id is None and 'parent_id' in request.args:
            # Explicitly filter for root sections (parent_id is None)
            query = query.filter(TextSection.parent_id.is_(None))
        
        sections = query.all()
        
        return jsonify({
            'sections': [section.to_dict() for section in sections]
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@texts_bp.route('/traditions', methods=['GET'])
def get_traditions():
    """Get all religious traditions"""
    try:
        traditions = db.session.query(ReligiousText.tradition).distinct().all()
        tradition_list = [tradition[0] for tradition in traditions]
        
        return jsonify({
            'traditions': tradition_list
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@texts_bp.route('/languages', methods=['GET'])
def get_languages():
    """Get all available languages"""
    try:
        # Get languages from text contents
        content_languages = db.session.query(TextContent.language).distinct().all()
        
        # Get original languages from religious texts
        original_languages = db.session.query(ReligiousText.original_language).distinct().all()
        
        # Combine and deduplicate
        all_languages = set()
        for lang in content_languages:
            if lang[0]:
                all_languages.add(lang[0])
        for lang in original_languages:
            if lang[0]:
                all_languages.add(lang[0])
        
        return jsonify({
            'languages': sorted(list(all_languages))
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500
