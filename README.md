# Religious Experiences App

A full-stack web application for studying religious experiences and texts, built with React TypeScript frontend and Flask Python backend.

## Project Structure

```
religious_experiences_app/
├── backend/                 # Python Flask backend
│   ├── src/
│   │   ├── models/         # Database models
│   │   ├── routes/         # API routes
│   │   └── main.py         # Flask app entry point
│   ├── instance/           # Database files
│   └── requirements.txt    # Python dependencies
├── frontend/               # React TypeScript frontend
│   ├── src/
│   │   ├── App.tsx         # Main React component
│   │   └── App.css         # Styles
│   └── package.json        # Node.js dependencies
├── scripts/                # Database and text processing scripts
│   ├── setup_database.py   # Initialize database
│   ├── import_texts.py     # Import religious texts
│   └── align_texts.py      # Create text alignments
└── texts/                  # Religious text data storage
```

## Features

- **Religious Text Management**: Store and manage religious texts from various traditions
- **Multi-language Support**: Handle texts in original languages with translations
- **Text Search**: Search through religious texts and translations
- **User Surveys**: Collect religious experience data from users
- **Text Alignment**: Cross-language text alignment for comparative study
- **RESTful API**: Complete backend API for all functionality

## Quick Start

### Prerequisites

- Python 3.8+ (tested with Python 3.13)
- Node.js 18+
- npm or yarn

### Easy Startup (Recommended)

**Windows:**
```bash
start.bat
```

**Linux/Mac:**
```bash
chmod +x start.sh
./start.sh
```

### Manual Setup

#### Backend Setup

1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Install Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Initialize the database (run from project root):
   ```bash
   python scripts/setup_database.py
   ```

4. Start the Flask server:
   ```bash
   cd backend
   python src/main.py
   ```

The backend will be running at `http://localhost:5000`

#### Frontend Setup

1. Navigate to the frontend directory:
   ```bash
   cd frontend
   ```

2. Install Node.js dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm run dev
   ```

The frontend will be running at `http://localhost:5173` (or next available port)

### ✅ Project Status

The project is now **fully functional** with:
- ✅ Backend API running on Flask
- ✅ Frontend React app with TypeScript
- ✅ Database initialized with sample data
- ✅ All major structural issues fixed
- ✅ Working religious texts display
- ✅ Functional survey system
- ✅ CORS properly configured

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - User login
- `GET /api/auth/profile` - Get user profile
- `PUT /api/auth/profile` - Update user profile

### Religious Texts
- `GET /api/texts/` - Get all religious texts
- `GET /api/texts/{id}` - Get specific text
- `GET /api/texts/{id}/contents` - Get text contents
- `GET /api/texts/{id}/translations` - Get text translations
- `GET /api/texts/{id}/sections` - Get text sections
- `GET /api/texts/traditions` - Get all traditions
- `GET /api/texts/languages` - Get all languages

### Search
- `GET /api/search/texts` - Search through texts
- `GET /api/search/sections` - Search through sections
- `GET /api/search/translations` - Search through translations

### Surveys
- `POST /api/survey/` - Submit survey
- `GET /api/survey/` - Get user's surveys
- `GET /api/survey/{id}` - Get specific survey
- `PUT /api/survey/{id}` - Update survey
- `DELETE /api/survey/{id}` - Delete survey
- `GET /api/survey/stats` - Get survey statistics

## Database Schema

The application uses SQLite with the following main tables:

- **users** - User accounts and authentication
- **religious_texts** - Metadata for religious texts
- **text_contents** - Actual text content in various languages
- **text_translations** - Translation mappings between languages
- **text_sections** - Hierarchical text organization (books, chapters, verses)
- **surveys** - User religious experience surveys

## Development

### Adding Religious Texts

1. Place HTML files in `texts/{tradition}/{language}/extracted/`
2. Run the import script:
   ```bash
   python scripts/import_texts.py
   ```

### Creating Text Alignments

After importing texts, create alignments:
```bash
python scripts/align_texts.py
```

### Frontend Development

The frontend uses:
- React 19 with TypeScript
- React Router for navigation
- Axios for API calls
- CSS for styling

### Backend Development

The backend uses:
- Flask web framework
- SQLAlchemy ORM
- JWT for authentication
- CORS for cross-origin requests

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is for academic research purposes.

## Support

For issues and questions, please create an issue in the repository.
