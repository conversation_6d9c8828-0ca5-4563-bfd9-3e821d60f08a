import os
import sys
import json
import sqlite3
import logging
from bs4 import BeautifulSoup

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Use os.path.join for cross-platform compatibility
DB_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'backend', 'instance', 'religious_experiences.db')

def ensure_db_connection():
    """Ensure database connection and return connection object"""
    db_dir = os.path.dirname(DB_PATH)
    if not os.path.exists(db_dir):
        os.makedirs(db_dir, exist_ok=True)
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    return conn

def process_english_bible():
    """Process the English Bible HTML files"""
    logger.info("Processing English Bible from extracted files...")
    
    # Use os.path.join for cross-platform compatibility
    extract_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                              'texts', 'christian', 'english', 'extracted')
    
    # Create directory if it doesn't exist
    if not os.path.exists(extract_dir):
        logger.warning(f"Extraction directory not found: {extract_dir}")
        logger.info(f"Creating directory: {extract_dir}")
        os.makedirs(extract_dir, exist_ok=True)
        logger.warning("No HTML files found. Please extract the Bible HTML files to this directory.")
        return []
    
    # Process the HTML files
    books = []
    book_files = []
    
    # Find all HTML files
    for root, dirs, files in os.walk(extract_dir):
        for file in files:
            if file.endswith('.htm') or file.endswith('.html'):
                # Skip index files and non-book files
                if file in ['index.htm', 'index.html', 'webfaq.htm']:
                    continue
                if len(file) > 7 and not file[0:3].isalpha():
                    continue
                book_files.append(os.path.join(root, file))
    
    if not book_files:
        logger.warning("No HTML files found. Please extract the Bible HTML files to the extraction directory.")
        # Create sample data for testing
        books = create_sample_bible_data()
        return books
    
    # Sort the files to ensure correct order
    book_files.sort()
    
    # Process each book
    for book_file in book_files:
        book_name = os.path.basename(book_file).replace('.htm', '').replace('.html', '')
        
        # Skip book index files (e.g., GEN.htm when we have GEN01.htm, GEN02.htm, etc.)
        if len(book_name) <= 3 and any(os.path.basename(f).startswith(book_name) and 
                                      os.path.basename(f) != book_name + '.htm' and
                                      os.path.basename(f) != book_name + '.html'
                                      for f in book_files):
            continue
        
        try:
            with open(book_file, 'r', encoding='utf-8') as f:
                content = f.read()
        except UnicodeDecodeError:
            # Try with a different encoding if UTF-8 fails
            try:
                with open(book_file, 'r', encoding='latin-1') as f:
                    content = f.read()
            except Exception as e:
                logger.error(f"Error reading file {book_file}: {e}")
                continue
        
        # Parse HTML
        soup = BeautifulSoup(content, 'html.parser')
        
        # Extract book title
        title_elem = soup.find('title')
        title = title_elem.text if title_elem else book_name
        
        # Extract chapters and verses
        chapters = []
        chapter_divs = soup.find_all('div', class_='c')
        
        for chapter_div in chapter_divs:
            chapter_num = chapter_div.get('id', '').replace('c', '')
            
            if not chapter_num.isdigit():
                continue
                
            verses = []
            verse_spans = chapter_div.find_all('span', class_='v')
            
            for verse_span in verse_spans:
                verse_num = verse_span.get('id', '').replace('v', '')
                
                if not verse_num.isdigit():
                    continue
                
                # Get verse text (all text until next verse or end of chapter)
                verse_text = ''
                current = verse_span.next_sibling
                
                while current and (not isinstance(current, type(verse_span)) or 'v' not in current.get('class', [])):
                    if isinstance(current, str):
                        verse_text += current
                    elif current.name != 'span' or 'v' not in current.get('class', []):
                        verse_text += current.get_text()
                    
                    current = current.next_sibling
                    if not current:
                        break
                
                verses.append({
                    'number': int(verse_num),
                    'text': verse_text.strip()
                })
            
            chapters.append({
                'number': int(chapter_num),
                'verses': verses
            })
        
        books.append({
            'name': book_name,
            'title': title,
            'chapters': chapters
        })
    
    # Save processed data to JSON
    output_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                              'texts', 'christian', 'english', 'web_bible_processed.json')
    
    # Ensure directory exists
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(books, f, ensure_ascii=False, indent=2)
    
    logger.info(f"Processed English Bible saved to {output_path}")
    
    return books

def process_hebrew_bible():
    """Process the Hebrew Bible HTML files"""
    logger.info("Processing Hebrew Bible from extracted files...")
    
    extract_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                              'texts', 'christian', 'original', 'extracted')
    
    # Create directory if it doesn't exist
    if not os.path.exists(extract_dir):
        logger.warning(f"Extraction directory not found: {extract_dir}")
        logger.info(f"Creating directory: {extract_dir}")
        os.makedirs(extract_dir, exist_ok=True)
        logger.warning("No HTML files found. Please extract the Hebrew Bible HTML files to this directory.")
        return []
    
    # Process the HTML files
    books = []
    book_files = []
    
    # Find all HTML files
    for root, dirs, files in os.walk(extract_dir):
        for file in files:
            if file.endswith('.htm') or file.endswith('.html'):
                # Skip index files and non-book files
                if file in ['index.htm', 'index.html']:
                    continue
                if len(file) > 7 and not file[0:3].isalpha():
                    continue
                book_files.append(os.path.join(root, file))
    
    if not book_files:
        logger.warning("No HTML files found. Please extract the Hebrew Bible HTML files to the extraction directory.")
        # Create sample data for testing
        books = create_sample_hebrew_bible_data()
        return books
    
    # Sort the files to ensure correct order
    book_files.sort()
    
    # Process each book
    for book_file in book_files:
        book_name = os.path.basename(book_file).replace('.htm', '').replace('.html', '')
        
        # Skip book index files (e.g., GEN.htm when we have GEN01.htm, GEN02.htm, etc.)
        if len(book_name) <= 3 and any(os.path.basename(f).startswith(book_name) and 
                                      os.path.basename(f) != book_name + '.htm' and
                                      os.path.basename(f) != book_name + '.html'
                                      for f in book_files):
            continue
        
        try:
            with open(book_file, 'r', encoding='utf-8') as f:
                content = f.read()
        except UnicodeDecodeError:
            # Try with a different encoding if UTF-8 fails
            try:
                with open(book_file, 'r', encoding='latin-1') as f:
                    content = f.read()
            except Exception as e:
                logger.error(f"Error reading file {book_file}: {e}")
                continue
        
        # Parse HTML
        soup = BeautifulSoup(content, 'html.parser')
        
        # Extract book title
        title_elem = soup.find('title')
        title = title_elem.text if title_elem else book_name
        
        # Extract chapters and verses
        chapters = []
        chapter_divs = soup.find_all('div', class_='c')
        
        for chapter_div in chapter_divs:
            chapter_num = chapter_div.get('id', '').replace('c', '')
            
            if not chapter_num.isdigit():
                continue
                
            verses = []
            verse_spans = chapter_div.find_all('span', class_='v')
            
            for verse_span in verse_spans:
                verse_num = verse_span.get('id', '').replace('v', '')
                
                if not verse_num.isdigit():
                    continue
                
                # Get verse text (all text until next verse or end of chapter)
                verse_text = ''
                current = verse_span.next_sibling
                
                while current and (not isinstance(current, type(verse_span)) or 'v' not in current.get('class', [])):
                    if isinstance(current, str):
                        verse_text += current
                    elif current.name != 'span' or 'v' not in current.get('class', []):
                        verse_text += current.get_text()
                    
                    current = current.next_sibling
                    if not current:
                        break
                
                verses.append({
                    'number': int(verse_num),
                    'text': verse_text.strip()
                })
            
            chapters.append({
                'number': int(chapter_num),
                'verses': verses
            })
        
        books.append({
            'name': book_name,
            'title': title,
            'chapters': chapters
        })
    
    # Save processed data to JSON
    output_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                              'texts', 'christian', 'original', 'hebrew_bible_processed.json')
    
    # Ensure directory exists
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(books, f, ensure_ascii=False, indent=2)
    
    logger.info(f"Processed Hebrew Bible saved to {output_path}")
    
    return books

def create_sample_bible_data():
    """Create sample Bible data for testing"""
    logger.info("Creating sample Bible data for testing")
    
    books = [
        {
            'name': 'GEN',
            'title': 'Genesis',
            'chapters': [
                {
                    'number': 1,
                    'verses': [
                        {'number': 1, 'text': 'In the beginning God created the heaven and the earth.'},
                        {'number': 2, 'text': 'And the earth was without form, and void; and darkness was upon the face of the deep. And the Spirit of God moved upon the face of the waters.'},
                        {'number': 3, 'text': 'And God said, Let there be light: and there was light.'}
                    ]
                },
                {
                    'number': 2,
                    'verses': [
                        {'number': 1, 'text': 'Thus the heavens and the earth were finished, and all the host of them.'},
                        {'number': 2, 'text': 'And on the seventh day God ended his work which he had made; and he rested on the seventh day from all his work which he had made.'},
                        {'number': 3, 'text': 'And God blessed the seventh day, and sanctified it: because that in it he had rested from all his work which God created and made.'}
                    ]
                }
            ]
        },
        {
            'name': 'EXO',
            'title': 'Exodus',
            'chapters': [
                {
                    'number': 1,
                    'verses': [
                        {'number': 1, 'text': 'Now these are the names of the children of Israel, which came into Egypt; every man and his household came with Jacob.'},
                        {'number': 2, 'text': 'Reuben, Simeon, Levi, and Judah,'},
                        {'number': 3, 'text': 'Issachar, Zebulun, and Benjamin,'}
                    ]
                }
            ]
        }
    ]
    
    return books

def create_sample_hebrew_bible_data():
    """Create sample Hebrew Bible data for testing"""
    logger.info("Creating sample Hebrew Bible data for testing")
    
    books = [
        {
            'name': 'GEN',
            'title': 'בראשית',
            'chapters': [
                {
                    'number': 1,
                    'verses': [
                        {'number': 1, 'text': 'בְּרֵאשִׁית בָּרָא אֱלֹהִים אֵת הַשָּׁמַיִם וְאֵת הָאָרֶץ'},
                        {'number': 2, 'text': 'וְהָאָרֶץ הָיְתָה תֹהוּ וָבֹהוּ וְחֹשֶׁךְ עַל־פְּנֵי תְהוֹם וְרוּחַ אֱלֹהִים מְרַחֶפֶת עַל־פְּנֵי הַמָּיִם'},
                        {'number': 3, 'text': 'וַיֹּאמֶר אֱלֹהִים יְהִי אוֹר וַיְהִי־אוֹר'}
                    ]
                },
                {
                    'number': 2,
                    'verses': [
                        {'number': 1, 'text': 'וַיְכֻלּוּ הַשָּׁמַיִם וְהָאָרֶץ וְכָל־צְבָאָם'},
                        {'number': 2, 'text': 'וַיְכַל אֱלֹהִים בַּיּוֹם הַשְּׁבִיעִי מְלַאכְתּוֹ אֲשֶׁר עָשָׂה וַיִּשְׁבֹּת בַּיּוֹם הַשְּׁבִיעִי מִכָּל־מְלַאכְתּוֹ אֲשֶׁר עָשָׂה'},
                        {'number': 3, 'text': 'וַיְבָרֶךְ אֱלֹהִים אֶת־יוֹם הַשְּׁבִיעִי וַיְקַדֵּשׁ אֹתוֹ כִּי בוֹ שָׁבַת מִכָּל־מְלַאכְתּוֹ אֲשֶׁר־בָּרָא אֱלֹהִים לַעֲשׂוֹת'}
                    ]
                }
            ]
        },
        {
            'name': 'EXO',
            'title': 'שמות',
            'chapters': [
                {
                    'number': 1,
                    'verses': [
                        {'number': 1, 'text': 'וְאֵלֶּה שְׁמוֹת בְּנֵי יִשְׂרָאֵל הַבָּאִים מִצְרָיְמָה אֵת יַעֲקֹב אִישׁ וּבֵיתוֹ בָּאוּ'},
                        {'number': 2, 'text': 'רְאוּבֵן שִׁמְעוֹן לֵוִי וִיהוּדָה'},
                        {'number': 3, 'text': 'יִשָּׂשכָר זְבוּלֻן וּבִנְיָמִן'}
                    ]
                }
            ]
        }
    ]
    
    return books

def import_bible_texts_to_db():
    """Import processed Bible texts into the database"""
    logger.info("Importing Bible texts into database...")
    
    # Get Bible ID from database
    conn = ensure_db_connection()
    cursor = conn.cursor()
    
    cursor.execute("SELECT id FROM religious_texts WHERE title = ?", ("Bible",))
    result = cursor.fetchone()
    
    if not result:
        logger.error("Bible not found in religious_texts table")
        logger.info("Make sure to run setup_database.py first")
        conn.close()
        return
    
    bible_id = result['id']
    
    # Process and import English Bible
    english_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                              'texts', 'christian', 'english', 'web_bible_processed.json')
    
    if not os.path.exists(english_path):
        logger.info("Processed English Bible not found. Processing from extracted files...")
        english_books = process_english_bible()
    else:
        with open(english_path, 'r', encoding='utf-8') as f:
            english_books = json.load(f)
    
    # Process and import Hebrew Bible
    hebrew_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                             'texts', 'christian', 'original', 'hebrew_bible_processed.json')
    
    if not os.path.exists(hebrew_path):
        logger.info("Processed Hebrew Bible not found. Processing from extracted files...")
        hebrew_books = process_hebrew_bible()
    else:
        with open(hebrew_path, 'r', encoding='utf-8') as f:
            hebrew_books = json.load(f)
    
    # Import English Bible content
    english_content_json = json.dumps(english_books, ensure_ascii=False)
    english_metadata = json.dumps({
        'source': 'World English Bible',
        'version': '1.0',
        'format': 'json'
    })
    
    cursor.execute("""
        INSERT INTO text_contents (text_id, language, content_type, content, metadata)
        VALUES (?, ?, ?, ?, ?)
    """, (bible_id, 'English', 'full', english_content_json, english_metadata))
    
    english_content_id = cursor.lastrowid
    logger.info(f"Imported English Bible content with ID: {english_content_id}")
    
    # Import Hebrew Bible content
    hebrew_content_json = json.dumps(hebrew_books, ensure_ascii=False)
    hebrew_metadata = json.dumps({
        'source': 'Hebrew Masoretic Text',
        'version': '1.0',
        'format': 'json'
    })
    
    cursor.execute("""
        INSERT INTO text_contents (text_id, language, content_type, content, metadata)
        VALUES (?, ?, ?, ?, ?)
    """, (bible_id, 'Hebrew', 'full', hebrew_content_json, hebrew_metadata))
    
    hebrew_content_id = cursor.lastrowid
    logger.info(f"Imported Hebrew Bible content with ID: {hebrew_content_id}")
    
    # Create translation record
    cursor.execute("""
        INSERT INTO text_translations (text_id, source_language, target_language, translator, translation_date, content, alignment_data)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    """, (bible_id, 'Hebrew', 'English', 'World English Bible', '2000', 
         'World English Bible translation', json.dumps({'type': 'line-by-line'})))
    
    translation_id = cursor.lastrowid
    logger.info(f"Created translation record with ID: {translation_id}")
    
    # Import book sections
    for english_book in english_books:
        book_name = english_book['name']
        
        # Insert English book section
        cursor.execute("""
            INSERT INTO text_sections (text_id, parent_id, section_type, section_number, title, metadata)
            VALUES (?, NULL, ?, ?, ?, ?)
        """, (bible_id, 'book', book_name, english_book['title'], json.dumps({'language': 'English'})))
        
        english_book_id = cursor.lastrowid
        
        # Find corresponding Hebrew book
        hebrew_book = next((b for b in hebrew_books if b['name'].upper() == book_name.upper()), None)
        
        if hebrew_book:
            # Insert Hebrew book section
            cursor.execute("""
                INSERT INTO text_sections (text_id, parent_id, section_type, section_number, title, metadata)
                VALUES (?, NULL, ?, ?, ?, ?)
            """, (bible_id, 'book', hebrew_book['name'], hebrew_book['title'], json.dumps({'language': 'Hebrew'})))
            
            hebrew_book_id = cursor.lastrowid
            
            # Process chapters
            for english_chapter in english_book['chapters']:
                chapter_num = english_chapter['number']
                
                # Insert English chapter section
                cursor.execute("""
                    INSERT INTO text_sections (text_id, parent_id, section_type, section_number, title, metadata)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (bible_id, english_book_id, 'chapter', str(chapter_num), 
                     f"Chapter {chapter_num}", json.dumps({'language': 'English'})))
                
                english_chapter_id = cursor.lastrowid
                
                # Find corresponding Hebrew chapter
                hebrew_chapter = next((c for c in hebrew_book['chapters'] 
                                    if c['number'] == chapter_num), None)
                
                if hebrew_chapter:
                    # Insert Hebrew chapter section
                    cursor.execute("""
                        INSERT INTO text_sections (text_id, parent_id, section_type, section_number, title, metadata)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (bible_id, hebrew_book_id, 'chapter', str(chapter_num), 
                         f"Chapter {chapter_num}", json.dumps({'language': 'Hebrew'})))
                    
                    hebrew_chapter_id = cursor.lastrowid
                    
                    # Process verses
                    for english_verse in english_chapter['verses']:
                        verse_num = english_verse['number']
                        
                        # Insert English verse section
                        cursor.execute("""
                            INSERT INTO text_sections (text_id, parent_id, section_type, section_number, title, metadata)
                            VALUES (?, ?, ?, ?, ?, ?)
                        """, (bible_id, english_chapter_id, 'verse', str(verse_num), 
                             english_verse['text'][:50] + '...' if len(english_verse['text']) > 50 else english_verse['text'], 
                             json.dumps({'language': 'English', 'text': english_verse['text']})))
                        
                        english_verse_id = cursor.lastrowid
                        
                        # Find corresponding Hebrew verse
                        hebrew_verse = next((v for v in hebrew_chapter['verses'] 
                                           if v['number'] == verse_num), None)
                        
                        if hebrew_verse:
                            # Insert Hebrew verse section
                            cursor.execute("""
                                INSERT INTO text_sections (text_id, parent_id, section_type, section_number, title, metadata)
                                VALUES (?, ?, ?, ?, ?, ?)
                            """, (bible_id, hebrew_chapter_id, 'verse', str(verse_num), 
                                 hebrew_verse['text'][:50] + '...' if len(hebrew_verse['text']) > 50 else hebrew_verse['text'], 
                                 json.dumps({'language': 'Hebrew', 'text': hebrew_verse['text']})))
                            
                            hebrew_verse_id = cursor.lastrowid
                            
                            # Create alignment between verses
                            cursor.execute("""
                                INSERT INTO text_alignments (translation_id, source_section_id, target_section_id, alignment_type, metadata)
                                VALUES (?, ?, ?, ?, ?)
                            """, (translation_id, hebrew_verse_id, english_verse_id, 'line-by-line', 
                                 json.dumps({
                                     'source_text': hebrew_verse['text'],
                                     'target_text': english_verse['text']
                                 })))
    
    conn.commit()
    conn.close()
    
    logger.info("Bible texts imported successfully")

def main():
    """Main function"""
    try:
        import_bible_texts_to_db()
    except Exception as e:
        logger.error(f"Error importing Bible texts: {e}")
        logger.info("Make sure to run setup_database.py first")

if __name__ == "__main__":
    main()
