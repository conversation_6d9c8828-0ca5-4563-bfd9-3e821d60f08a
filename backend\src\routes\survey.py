from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity, get_jwt
from src.models import db, Survey, User

survey_bp = Blueprint('survey', __name__)


@survey_bp.route('/', methods=['POST'])
def create_survey():
    """Create a new survey response"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Get user ID if authenticated, otherwise allow anonymous surveys
        user_id = None
        try:
            user_id = get_jwt_identity()
        except:
            pass  # Anonymous survey
        
        # Create survey
        survey = Survey(
            user_id=user_id,
            religious_affiliation=data.get('religious_affiliation'),
            geographical_location=data.get('geographical_location'),
            divine_experience=data.get('divine_experience'),
            divine_experience_frequency=data.get('divine_experience_frequency'),
            divine_experience_significance=data.get('divine_experience_significance'),
            truth_perception=data.get('truth_perception'),
            truth_confidence_level=data.get('truth_confidence_level'),
            practice_details=data.get('practice_details'),
            practice_frequency=data.get('practice_frequency'),
            practice_importance=data.get('practice_importance'),
            additional_comments=data.get('additional_comments')
        )
        
        db.session.add(survey)
        db.session.commit()
        
        return jsonify({
            'message': 'Survey submitted successfully',
            'survey_id': survey.id
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500


@survey_bp.route('/', methods=['GET'])
@jwt_required()
def get_surveys():
    """Get surveys (admin only or user's own surveys)"""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # For now, users can only see their own surveys
        # In the future, you might want to add admin roles
        surveys = Survey.query.filter_by(user_id=user_id).all()
        
        return jsonify({
            'surveys': [survey.to_dict() for survey in surveys]
        }), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@survey_bp.route('/<int:survey_id>', methods=['GET'])
@jwt_required()
def get_survey(survey_id):
    """Get a specific survey"""
    try:
        user_id = get_jwt_identity()
        survey = Survey.query.get(survey_id)
        
        if not survey:
            return jsonify({'error': 'Survey not found'}), 404
        
        # Users can only view their own surveys
        if survey.user_id != user_id:
            return jsonify({'error': 'Access denied'}), 403
        
        return jsonify({'survey': survey.to_dict()}), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@survey_bp.route('/<int:survey_id>', methods=['PUT'])
@jwt_required()
def update_survey(survey_id):
    """Update a survey"""
    try:
        user_id = get_jwt_identity()
        survey = Survey.query.get(survey_id)
        
        if not survey:
            return jsonify({'error': 'Survey not found'}), 404
        
        # Users can only update their own surveys
        if survey.user_id != user_id:
            return jsonify({'error': 'Access denied'}), 403
        
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Update survey fields
        updatable_fields = [
            'religious_affiliation', 'geographical_location', 'divine_experience',
            'divine_experience_frequency', 'divine_experience_significance',
            'truth_perception', 'truth_confidence_level', 'practice_details',
            'practice_frequency', 'practice_importance', 'additional_comments'
        ]
        
        for field in updatable_fields:
            if field in data:
                setattr(survey, field, data[field])
        
        db.session.commit()
        
        return jsonify({
            'message': 'Survey updated successfully',
            'survey': survey.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500


@survey_bp.route('/<int:survey_id>', methods=['DELETE'])
@jwt_required()
def delete_survey(survey_id):
    """Delete a survey"""
    try:
        user_id = get_jwt_identity()
        survey = Survey.query.get(survey_id)
        
        if not survey:
            return jsonify({'error': 'Survey not found'}), 404
        
        # Users can only delete their own surveys
        if survey.user_id != user_id:
            return jsonify({'error': 'Access denied'}), 403
        
        db.session.delete(survey)
        db.session.commit()
        
        return jsonify({'message': 'Survey deleted successfully'}), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500


@survey_bp.route('/stats', methods=['GET'])
def get_survey_stats():
    """Get anonymous survey statistics"""
    try:
        total_surveys = Survey.query.count()
        
        # Get religious affiliation distribution
        affiliation_stats = db.session.query(
            Survey.religious_affiliation,
            db.func.count(Survey.id).label('count')
        ).group_by(Survey.religious_affiliation).all()
        
        # Get experience frequency distribution
        frequency_stats = db.session.query(
            Survey.divine_experience_frequency,
            db.func.count(Survey.id).label('count')
        ).group_by(Survey.divine_experience_frequency).all()
        
        return jsonify({
            'total_surveys': total_surveys,
            'religious_affiliation_distribution': [
                {'affiliation': stat[0], 'count': stat[1]} 
                for stat in affiliation_stats if stat[0]
            ],
            'experience_frequency_distribution': [
                {'frequency': stat[0], 'count': stat[1]} 
                for stat in frequency_stats if stat[0]
            ]
        }), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500
