/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

/* App Layout */
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: transparent;
}

/* Navigation */
.navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  color: #333;
  padding: 1rem 0;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-logo {
  font-size: 1.8rem;
  font-weight: 700;
  color: #4a5568;
  text-decoration: none;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.nav-links {
  display: flex;
  gap: 1rem;
}

.nav-links a {
  color: #4a5568;
  text-decoration: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  transition: all 0.3s ease;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.nav-links a::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  transition: left 0.3s ease;
  z-index: -1;
}

.nav-links a:hover::before {
  left: 0;
}

.nav-links a:hover {
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* Main Content */
.main-content {
  flex: 1;
  max-width: 1400px;
  margin: 0 auto;
  padding: 3rem 2rem;
  width: 100%;
  box-sizing: border-box;
}

/* Home Page */
.home {
  text-align: center;
}

.hero-section {
  margin-bottom: 4rem;
}

.hero-section h1 {
  color: white;
  margin-bottom: 1.5rem;
  font-size: 3.5rem;
  font-weight: 700;
  text-shadow: 0 4px 20px rgba(0,0,0,0.3);
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-section > p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.3rem;
  margin-bottom: 3rem;
  text-shadow: 0 2px 10px rgba(0,0,0,0.2);
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: 3rem;
  margin-top: 2rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 1.5rem 2rem;
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 10px rgba(0,0,0,0.3);
  margin-bottom: 0.5rem;
}

.stat-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.texts-section {
  margin-top: 3rem;
}

.texts-section h2 {
  color: white;
  font-size: 2.5rem;
  margin-bottom: 2rem;
  text-shadow: 0 2px 15px rgba(0,0,0,0.3);
}

.texts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.text-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.text-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.text-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0,0,0,0.2);
  background: rgba(255, 255, 255, 1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  gap: 1rem;
}

.text-card h3 {
  color: #2d3748;
  margin: 0;
  font-size: 1.4rem;
  font-weight: 600;
  flex: 1;
}

.tradition-badge {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 0.4rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.no-texts-message {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
  margin: 2rem auto;
  max-width: 500px;
}

.no-texts-message p {
  color: #4a5568;
  font-size: 1.2rem;
  margin: 0;
}

.text-card .description {
  color: #4a5568;
  margin-bottom: 1.5rem;
  line-height: 1.6;
  font-size: 1rem;
}

.text-meta {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
  color: #718096;
  padding-top: 1rem;
  border-top: 1px solid rgba(113, 128, 150, 0.2);
}

.text-meta span {
  background: rgba(102, 126, 234, 0.1);
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-weight: 500;
}

/* Survey Page */
.survey {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.survey h1 {
  color: white;
  margin-bottom: 1rem;
  font-size: 3rem;
  font-weight: 700;
  text-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.survey > p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.2rem;
  margin-bottom: 3rem;
  text-shadow: 0 2px 10px rgba(0,0,0,0.2);
}

.survey-form {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
  text-align: left;
}

.form-group {
  margin-bottom: 2rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: #2d3748;
  font-size: 1.1rem;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 1rem;
  border: 2px solid rgba(102, 126, 234, 0.2);
  border-radius: 12px;
  font-size: 1rem;
  box-sizing: border-box;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.8);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: white;
  transform: translateY(-2px);
}

.submit-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 1rem 3rem;
  border: none;
  border-radius: 25px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  width: 100%;
  margin-top: 1rem;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
}

.submit-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.survey-success {
  text-align: center;
  padding: 3rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

.survey-success h2 {
  color: #48bb78;
  margin-bottom: 1rem;
  font-size: 2.5rem;
  font-weight: 700;
}

.survey-success p {
  color: #4a5568;
  font-size: 1.2rem;
  margin-bottom: 2rem;
}

.survey-success a {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  padding: 0.75rem 2rem;
  border: 2px solid #667eea;
  border-radius: 25px;
  transition: all 0.3s ease;
  display: inline-block;
}

.survey-success a:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
}

/* Footer */
.footer {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
  padding: 2rem;
  margin-top: auto;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer p {
  margin: 0;
  font-size: 1rem;
  text-shadow: 0 2px 10px rgba(0,0,0,0.2);
}

/* Utility Classes */
.loading, .error {
  text-align: center;
  padding: 3rem;
  font-size: 1.3rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
  margin: 2rem auto;
  max-width: 500px;
}

.loading {
  color: #667eea;
  font-weight: 600;
}

.error {
  color: #e53e3e;
  font-weight: 600;
}

/* Loading Animation */
.loading::after {
  content: '';
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #667eea;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s ease-in-out infinite;
  margin-left: 10px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-container {
    padding: 0 1rem;
    flex-direction: column;
    gap: 1rem;
  }

  .nav-links {
    gap: 0.5rem;
  }

  .nav-links a {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }

  .main-content {
    padding: 2rem 1rem;
  }

  .hero-section h1 {
    font-size: 2.5rem;
  }

  .hero-section > p {
    font-size: 1.1rem;
  }

  .hero-stats {
    gap: 1.5rem;
  }

  .stat-item {
    padding: 1rem 1.5rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .texts-section h2 {
    font-size: 2rem;
  }

  .texts-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .text-card {
    padding: 1.5rem;
  }

  .text-meta {
    flex-direction: column;
    gap: 0.5rem;
  }

  .survey h1 {
    font-size: 2.5rem;
  }

  .survey-form {
    padding: 2rem;
  }

  .form-group input,
  .form-group textarea,
  .form-group select {
    padding: 0.75rem;
  }
}

@media (max-width: 480px) {
  .hero-section h1 {
    font-size: 2rem;
  }

  .hero-stats {
    flex-direction: column;
    gap: 1rem;
  }

  .stat-item {
    padding: 0.75rem 1rem;
  }

  .stat-number {
    font-size: 1.75rem;
  }

  .texts-section h2 {
    font-size: 1.75rem;
  }

  .survey h1 {
    font-size: 2rem;
  }

  .survey-form {
    padding: 1.5rem;
  }

  .text-card {
    padding: 1rem;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .tradition-badge {
    align-self: flex-start;
  }
}
