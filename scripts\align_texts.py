import os
import sys
import json
import sqlite3
import logging
from difflib import SequenceMatcher

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database connection
DB_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'backend', 'instance', 'religious_experiences.db')

def ensure_db_connection():
    """Ensure database connection and return connection object"""
    os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    return conn

def align_bible_texts():
    """Align English and Hebrew Bible texts"""
    logger.info("Aligning Bible texts...")
    
    # Load processed Bible texts
    english_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 
                              'texts', 'christian', 'english', 'web_bible_processed.json')
    hebrew_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 
                             'texts', 'christian', 'original', 'hebrew_bible_processed.json')
    
    if not os.path.exists(english_path) or not os.path.exists(hebrew_path):
        logger.error("Processed Bible texts not found")
        return
    
    with open(english_path, 'r', encoding='utf-8') as f:
        english_books = json.load(f)
    
    with open(hebrew_path, 'r', encoding='utf-8') as f:
        hebrew_books = json.load(f)
    
    # Create alignment data
    alignment_data = []
    
    for english_book in english_books:
        english_book_name = english_book['name']
        
        # Find corresponding Hebrew book
        hebrew_book = next((b for b in hebrew_books if b['name'].upper() == english_book_name.upper()), None)
        
        if not hebrew_book:
            logger.warning(f"No Hebrew book found for English book: {english_book_name}")
            continue
        
        # Align chapters
        for english_chapter in english_book['chapters']:
            chapter_num = english_chapter['number']
            
            # Find corresponding Hebrew chapter
            hebrew_chapter = next((c for c in hebrew_book['chapters'] if c['number'] == chapter_num), None)
            
            if not hebrew_chapter:
                continue
            
            # Align verses
            for english_verse in english_chapter['verses']:
                verse_num = english_verse['number']
                
                # Find corresponding Hebrew verse
                hebrew_verse = next((v for v in hebrew_chapter['verses'] if v['number'] == verse_num), None)
                
                if not hebrew_verse:
                    continue
                
                # Create alignment entry
                alignment_entry = {
                    'book': english_book_name,
                    'chapter': chapter_num,
                    'verse': verse_num,
                    'english_text': english_verse['text'],
                    'hebrew_text': hebrew_verse['text'],
                    'similarity': SequenceMatcher(None, english_verse['text'], hebrew_verse['text']).ratio()
                }
                
                alignment_data.append(alignment_entry)
    
    # Save alignment data to JSON
    output_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 
                              'texts', 'christian', 'bible_alignment.json')
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(alignment_data, f, ensure_ascii=False, indent=2)
    
    logger.info(f"Bible alignment data saved to {output_path}")
    
    # Insert alignment data into database
    logger.info("Inserting Bible alignment data into database...")
    
    conn = ensure_db_connection()
    cursor = conn.cursor()
    
    # Get Bible ID
    cursor.execute("SELECT id FROM religious_texts WHERE title = ?", ("Bible",))
    result = cursor.fetchone()
    
    if not result:
        logger.error("Bible not found in religious_texts table")
        conn.close()
        return
    
    text_id = result['id']
    
    # Get translation record
    cursor.execute("""
        SELECT id FROM text_translations 
        WHERE text_id = ? AND source_language = ? AND target_language = ?
    """, (text_id, "Hebrew", "English"))
    
    translation_result = cursor.fetchone()
    
    if translation_result:
        translation_id = translation_result['id']
        logger.info(f"Found existing translation record with ID: {translation_id}")
    else:
        # Insert translation record
        cursor.execute("""
            INSERT INTO text_translations 
            (text_id, source_language, target_language, translator, translation_date, content, alignment_data)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (text_id, "Hebrew", "English", "World English Bible", "2000", 
             "World English Bible translation", json.dumps({'type': 'line-by-line'})))
        
        translation_id = cursor.lastrowid
    
    # Get English and Hebrew content IDs
    cursor.execute("""
        SELECT id FROM text_contents 
        WHERE text_id = ? AND language = ? AND content_type = ?
    """, (text_id, "English", "full"))
    
    english_content = cursor.fetchone()
    
    cursor.execute("""
        SELECT id FROM text_contents 
        WHERE text_id = ? AND language = ? AND content_type = ?
    """, (text_id, "Hebrew", "full"))
    
    hebrew_content = cursor.fetchone()
    
    if not english_content or not hebrew_content:
        logger.error("English or Hebrew Bible content not found in text_contents table")
        conn.close()
        return
    
    # Update translation record with alignment data
    cursor.execute("""
        UPDATE text_translations 
        SET alignment_data = ?
        WHERE id = ?
    """, (json.dumps({'type': 'line-by-line', 'alignments': alignment_data}), translation_id))
    
    conn.commit()
    logger.info("Bible alignment data inserted into database")
    
    # Create paragraph-based alignments
    logger.info("Creating paragraph-based alignments...")
    
    paragraph_alignments = []
    current_paragraph = {
        'book': None,
        'chapter': None,
        'start_verse': None,
        'end_verse': None,
        'english_text': '',
        'hebrew_text': ''
    }
    
    for i, alignment in enumerate(alignment_data):
        # Start a new paragraph if book or chapter changes, or every 5 verses
        if (current_paragraph['book'] != alignment['book'] or 
            current_paragraph['chapter'] != alignment['chapter'] or 
            (current_paragraph['start_verse'] is not None and 
             alignment['verse'] - current_paragraph['end_verse'] > 1) or
            (current_paragraph['start_verse'] is not None and 
             alignment['verse'] - current_paragraph['start_verse'] >= 5)):
            
            # Save current paragraph if not empty
            if current_paragraph['start_verse'] is not None:
                paragraph_alignments.append(current_paragraph)
            
            # Start new paragraph
            current_paragraph = {
                'book': alignment['book'],
                'chapter': alignment['chapter'],
                'start_verse': alignment['verse'],
                'end_verse': alignment['verse'],
                'english_text': alignment['english_text'],
                'hebrew_text': alignment['hebrew_text']
            }
        else:
            # Continue current paragraph
            current_paragraph['end_verse'] = alignment['verse']
            current_paragraph['english_text'] += ' ' + alignment['english_text']
            current_paragraph['hebrew_text'] += ' ' + alignment['hebrew_text']
    
    # Add the last paragraph
    if current_paragraph['start_verse'] is not None:
        paragraph_alignments.append(current_paragraph)
    
    # Save paragraph alignments to JSON
    paragraph_output_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 
                                       'texts', 'christian', 'bible_paragraph_alignment.json')
    
    with open(paragraph_output_path, 'w', encoding='utf-8') as f:
        json.dump(paragraph_alignments, f, ensure_ascii=False, indent=2)
    
    logger.info(f"Paragraph alignments saved to {paragraph_output_path}")
    
    # Insert paragraph alignments into database
    logger.info("Inserting paragraph alignments into database...")
    
    # Check if translation record exists
    cursor.execute("""
        SELECT id FROM text_translations 
        WHERE text_id = ? AND source_language = ? AND target_language = ? AND id = ?
    """, (text_id, "Hebrew", "English", translation_id))
    
    translation_result = cursor.fetchone()
    
    if not translation_result:
        logger.error("Translation record not found")
        conn.close()
        return
    
    # Update translation record with paragraph alignment data
    cursor.execute("""
        UPDATE text_translations 
        SET alignment_data = ?
        WHERE id = ?
    """, (json.dumps({
        'type': 'paragraph',
        'alignments': paragraph_alignments
    }), translation_id))
    
    conn.commit()
    conn.close()
    
    logger.info("Paragraph alignments inserted into database")

def main():
    """Main function"""
    align_bible_texts()
    logger.info("All texts aligned successfully")

if __name__ == "__main__":
    main()
