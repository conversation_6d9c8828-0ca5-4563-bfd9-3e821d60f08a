from flask import Blueprint, request, jsonify
from sqlalchemy import or_, and_
from src.models import db, ReligiousText, TextContent, TextTranslation, TextSection

search_bp = Blueprint('search', __name__)


@search_bp.route('/texts', methods=['GET'])
def search_texts():
    """Search through religious texts"""
    try:
        query = request.args.get('q', '').strip()
        tradition = request.args.get('tradition')
        language = request.args.get('language')
        content_type = request.args.get('content_type')
        limit = min(int(request.args.get('limit', 50)), 100)  # Max 100 results
        offset = int(request.args.get('offset', 0))
        
        if not query:
            return jsonify({'error': 'Search query is required'}), 400
        
        # Build the search query
        search_conditions = []
        
        # Search in text contents
        content_query = TextContent.query.join(ReligiousText)
        
        # Add text search condition
        search_conditions.append(
            or_(
                TextContent.content.contains(query),
                ReligiousText.title.contains(query),
                ReligiousText.description.contains(query)
            )
        )
        
        # Add filters
        if tradition:
            search_conditions.append(ReligiousText.tradition == tradition)
        
        if language:
            search_conditions.append(TextContent.language == language)
        
        if content_type:
            search_conditions.append(TextContent.content_type == content_type)
        
        # Apply all conditions
        if search_conditions:
            content_query = content_query.filter(and_(*search_conditions))
        
        # Execute query with pagination
        results = content_query.offset(offset).limit(limit).all()
        
        # Format results
        search_results = []
        for content in results:
            result = {
                'text_id': content.text_id,
                'text_title': content.religious_text.title,
                'text_tradition': content.religious_text.tradition,
                'content_id': content.id,
                'language': content.language,
                'content_type': content.content_type,
                'content_snippet': content.content[:500] + '...' if len(content.content) > 500 else content.content,
                'metadata': content.get_metadata()
            }
            search_results.append(result)
        
        # Get total count for pagination
        total_count = content_query.count()
        
        return jsonify({
            'results': search_results,
            'total_count': total_count,
            'limit': limit,
            'offset': offset,
            'query': query
        }), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@search_bp.route('/sections', methods=['GET'])
def search_sections():
    """Search through text sections"""
    try:
        query = request.args.get('q', '').strip()
        text_id = request.args.get('text_id')
        section_type = request.args.get('section_type')
        limit = min(int(request.args.get('limit', 50)), 100)
        offset = int(request.args.get('offset', 0))
        
        if not query:
            return jsonify({'error': 'Search query is required'}), 400
        
        # Build the search query
        section_query = TextSection.query.join(ReligiousText)
        
        search_conditions = [
            or_(
                TextSection.title.contains(query),
                TextSection.content.contains(query),
                ReligiousText.title.contains(query)
            )
        ]
        
        # Add filters
        if text_id:
            search_conditions.append(TextSection.text_id == int(text_id))
        
        if section_type:
            search_conditions.append(TextSection.section_type == section_type)
        
        # Apply conditions
        section_query = section_query.filter(and_(*search_conditions))
        
        # Execute query with pagination
        results = section_query.offset(offset).limit(limit).all()
        
        # Format results
        search_results = []
        for section in results:
            result = {
                'section_id': section.id,
                'text_id': section.text_id,
                'text_title': section.religious_text.title,
                'section_type': section.section_type,
                'section_number': section.section_number,
                'title': section.title,
                'content_snippet': section.content[:300] + '...' if section.content and len(section.content) > 300 else section.content,
                'metadata': section.get_metadata()
            }
            search_results.append(result)
        
        # Get total count
        total_count = section_query.count()
        
        return jsonify({
            'results': search_results,
            'total_count': total_count,
            'limit': limit,
            'offset': offset,
            'query': query
        }), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@search_bp.route('/translations', methods=['GET'])
def search_translations():
    """Search through translations"""
    try:
        query = request.args.get('q', '').strip()
        source_language = request.args.get('source_language')
        target_language = request.args.get('target_language')
        limit = min(int(request.args.get('limit', 50)), 100)
        offset = int(request.args.get('offset', 0))
        
        if not query:
            return jsonify({'error': 'Search query is required'}), 400
        
        # Build the search query
        translation_query = TextTranslation.query.join(ReligiousText)
        
        search_conditions = [
            or_(
                TextTranslation.content.contains(query),
                ReligiousText.title.contains(query),
                TextTranslation.translator.contains(query)
            )
        ]
        
        # Add filters
        if source_language:
            search_conditions.append(TextTranslation.source_language == source_language)
        
        if target_language:
            search_conditions.append(TextTranslation.target_language == target_language)
        
        # Apply conditions
        translation_query = translation_query.filter(and_(*search_conditions))
        
        # Execute query with pagination
        results = translation_query.offset(offset).limit(limit).all()
        
        # Format results
        search_results = []
        for translation in results:
            result = {
                'translation_id': translation.id,
                'text_id': translation.text_id,
                'text_title': translation.religious_text.title,
                'source_language': translation.source_language,
                'target_language': translation.target_language,
                'translator': translation.translator,
                'translation_date': translation.translation_date,
                'content_snippet': translation.content[:500] + '...' if len(translation.content) > 500 else translation.content,
                'alignment_data': translation.get_alignment_data()
            }
            search_results.append(result)
        
        # Get total count
        total_count = translation_query.count()
        
        return jsonify({
            'results': search_results,
            'total_count': total_count,
            'limit': limit,
            'offset': offset,
            'query': query
        }), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500
