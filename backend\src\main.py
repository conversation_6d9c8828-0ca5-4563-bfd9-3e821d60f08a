import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))  # DON'T CHANGE THIS !!!

from flask import Flask, jsonify, request, send_from_directory
from flask_cors import CORS
from flask_jwt_extended import J<PERSON><PERSON>anager, create_access_token, jwt_required, get_jwt_identity
from src.models import db
from src.routes import auth_bp, texts_bp, search_bp, survey_bp
import json

app = Flask(__name__)
CORS(app)

# Configure database
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///religious_experiences.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['JWT_SECRET_KEY'] = 'your-secret-key'  # Change this in production

# Initialize extensions
db.init_app(app)
jwt = JWTManager(app)

# Register blueprints
app.register_blueprint(auth_bp, url_prefix='/api/auth')
app.register_blueprint(texts_bp, url_prefix='/api/texts')
app.register_blueprint(search_bp, url_prefix='/api/search')
app.register_blueprint(survey_bp, url_prefix='/api/survey')

# Create database tables
@app.before_first_request
def create_tables():
    db.create_all()
    # Add sample data if database is empty
    from src.models.religious_text import ReligiousText, TextContent, TextTranslation
    from src.models.user import User
    
    if not ReligiousText.query.first():
        # Add sample religious texts
        add_sample_data()

def add_sample_data():
    from src.models.religious_text import ReligiousText, TextContent, TextTranslation
    from src.models.user import User
    
    # Add sample users
    if not User.query.filter_by(email='<EMAIL>').first():
        admin = User(
            username='admin',
            email='<EMAIL>',
            password='password'  # In production, use password hashing
        )
        db.session.add(admin)
    
    # Add sample religious texts
    religions = [
        {
            'name': 'Christianity',
            'texts': [
                {
                    'title': 'Bible',
                    'description': 'The Christian Bible',
                    'tradition': 'Christianity',
                    'year_composed': '1st-4th century CE',
                    'original_language': 'Hebrew/Greek',
                    'path': 'texts/christian'
                }
            ]
        },
        {
            'name': 'Judaism',
            'texts': [
                {
                    'title': 'Torah',
                    'description': 'The first five books of the Hebrew Bible',
                    'tradition': 'Judaism',
                    'year_composed': '6th-5th century BCE',
                    'original_language': 'Hebrew',
                    'path': 'texts/hebrew'
                }
            ]
        },
        {
            'name': 'Islam',
            'texts': [
                {
                    'title': 'Quran',
                    'description': 'The central religious text of Islam',
                    'tradition': 'Islam',
                    'year_composed': '7th century CE',
                    'original_language': 'Arabic',
                    'path': 'texts/islamic'
                }
            ]
        },
        {
            'name': 'Hinduism',
            'texts': [
                {
                    'title': 'Bhagavad Gita',
                    'description': 'A 700-verse Hindu scripture that is part of the epic Mahabharata',
                    'tradition': 'Hinduism',
                    'year_composed': '5th-2nd century BCE',
                    'original_language': 'Sanskrit',
                    'path': 'texts/hindu'
                },
                {
                    'title': 'Rigveda',
                    'description': 'An ancient Indian collection of Vedic Sanskrit hymns',
                    'tradition': 'Hinduism',
                    'year_composed': '1500-1200 BCE',
                    'original_language': 'Sanskrit',
                    'path': 'texts/hindu'
                }
            ]
        },
        {
            'name': 'Buddhism',
            'texts': [
                {
                    'title': 'Dhammapada',
                    'description': 'A collection of sayings of the Buddha in verse form',
                    'tradition': 'Buddhism',
                    'year_composed': '3rd century BCE',
                    'original_language': 'Pali',
                    'path': 'texts/buddhist'
                }
            ]
        },
        {
            'name': 'Gnosticism',
            'texts': [
                {
                    'title': 'Nag Hammadi Library',
                    'description': 'A collection of Gnostic texts discovered near Nag Hammadi, Egypt',
                    'tradition': 'Gnosticism',
                    'year_composed': '2nd-4th century CE',
                    'original_language': 'Coptic',
                    'path': 'texts/gnostic'
                }
            ]
        },
        {
            'name': 'Zoroastrianism',
            'texts': [
                {
                    'title': 'Avesta',
                    'description': 'The primary collection of religious texts of Zoroastrianism',
                    'tradition': 'Zoroastrianism',
                    'year_composed': '6th century BCE',
                    'original_language': 'Avestan',
                    'path': 'texts/zoroastrian'
                }
            ]
        }
    ]
    
    for religion in religions:
        for text_info in religion['texts']:
            text = ReligiousText(
                title=text_info['title'],
                description=text_info['description'],
                tradition=text_info['tradition'],
                year_composed=text_info['year_composed'],
                original_language=text_info['original_language'],
                path=text_info['path']
            )
            db.session.add(text)
    
    db.session.commit()
    
    # Now add sample content and translations
    texts = ReligiousText.query.all()
    for text in texts:
        # Add sample content
        content = TextContent(
            text_id=text.id,
            language=text.original_language,
            content_type='original',
            content='Sample content for ' + text.title + ' in ' + text.original_language,
            metadata=json.dumps({
                'source': 'Sample data',
                'version': '1.0'
            })
        )
        db.session.add(content)
        
        # Add sample translation
        translation = TextTranslation(
            text_id=text.id,
            source_language=text.original_language,
            target_language='English',
            translator='Sample Translator',
            translation_date='2025',
            content='Sample translation of ' + text.title + ' from ' + text.original_language + ' to English',
            alignment_data=json.dumps({
                'type': 'line-by-line',
                'mapping': [
                    {'source_id': 1, 'target_id': 1},
                    {'source_id': 2, 'target_id': 2}
                ]
            })
        )
        db.session.add(translation)
    
    db.session.commit()

@app.route('/')
def index():
    return jsonify({
        'message': 'Welcome to the Religious Experiences API',
        'endpoints': {
            'auth': '/api/auth',
            'texts': '/api/texts',
            'search': '/api/search',
            'survey': '/api/survey'
        }
    })

@app.route('/static/<path:path>')
def serve_static(path):
    return send_from_directory('static', path)

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
